# مخططات تدفق نظام RAG المحلي

## 1. مخطط تدفق العمليات الأساسي

```mermaid
flowchart TD
    A[المستخدم يطرح سؤال] --> B[معالجة السؤال]
    B --> C[تحويل السؤال إلى Vector]
    C --> D[البحث في قاعدة البيانات الشعاعية]
    D --> E[استرجاع أقرب النصوص المطابقة]
    E --> F[تجميع النصوص المسترجعة]
    F --> G[إرسال السؤال + النصوص للـ LLM]
    G --> H[توليد الإجابة النهائية]
    H --> I[عرض الإجابة للمستخدم]
    
    %% إعداد النظام
    J[إضافة مستندات جديدة] --> K[قراءة الملفات]
    K --> L{نوع الملف؟}
    L -->|PDF| M[استخراج النص من PDF]
    L -->|Word| N[استخراج النص من Word]
    L -->|Text| O[قراءة النص مباشرة]
    
    M --> P[تقسيم النص إلى قطع صغيرة]
    N --> P
    O --> P
    
    P --> Q[تحويل كل قطعة إلى Vector]
    Q --> R[حفظ في قاعدة البيانات الشعاعية]
    R --> S[النظام جاهز للاستعلام]
    
    %% ربط العمليات
    S -.-> A
    
    %% تنسيق الألوان
    classDef userAction fill:#e1f5fe
    classDef processing fill:#f3e5f5
    classDef storage fill:#e8f5e8
    classDef llm fill:#fff3e0
    
    class A,J userAction
    class B,C,K,L,M,N,O,P,Q processing
    class D,R,S storage
    class G,H llm
```

## 2. البنية المعمارية للنظام

```mermaid
graph TB
    subgraph "طبقة الواجهة"
        UI[واجهة المستخدم CLI/Web]
    end
    
    subgraph "طبقة التحكم الرئيسية"
        RAG[RAG System Controller]
    end
    
    subgraph "طبقة معالجة المستندات"
        DP[Document Processor]
        PDF[PDF Reader]
        DOCX[Word Reader]
        TXT[Text Reader]
        
        DP --> PDF
        DP --> DOCX
        DP --> TXT
    end
    
    subgraph "طبقة التشفير والبحث"
        EMB[Embedding Model]
        VS[Vector Store]
        SEARCH[Search Engine]
        
        EMB --> VS
        VS --> SEARCH
    end
    
    subgraph "طبقة الذكاء الاصطناعي"
        LLM[Local LLM Model]
        PROMPT[Prompt Template]
        
        PROMPT --> LLM
    end
    
    subgraph "طبقة التخزين"
        VDB[(Vector Database)]
        CONFIG[Configuration Files]
        MODELS[Model Files]
    end
    
    %% الاتصالات
    UI --> RAG
    RAG --> DP
    RAG --> EMB
    RAG --> SEARCH
    RAG --> LLM
    
    DP --> EMB
    EMB --> VDB
    SEARCH --> VDB
    LLM --> UI
    
    CONFIG -.-> RAG
    MODELS -.-> LLM
    MODELS -.-> EMB
    
    %% تنسيق الألوان
    classDef interface fill:#e3f2fd
    classDef control fill:#f1f8e9
    classDef processing fill:#fce4ec
    classDef ai fill:#fff8e1
    classDef storage fill:#f3e5f5
    
    class UI interface
    class RAG control
    class DP,PDF,DOCX,TXT,EMB,VS,SEARCH processing
    class LLM,PROMPT ai
    class VDB,CONFIG,MODELS storage
```

## 3. دورة حياة البيانات في النظام

```mermaid
sequenceDiagram
    participant U as المستخدم
    participant UI as الواجهة
    participant RAG as نظام RAG
    participant DP as معالج المستندات
    participant EMB as نموذج التشفير
    participant VDB as قاعدة البيانات
    participant LLM as نموذج اللغة
    
    Note over U,LLM: مرحلة إضافة المستندات
    U->>UI: رفع ملفات جديدة
    UI->>RAG: طلب معالجة الملفات
    RAG->>DP: قراءة وتنظيف النصوص
    DP->>DP: تقسيم النص إلى chunks
    DP->>EMB: تحويل النصوص إلى vectors
    EMB->>VDB: حفظ البيانات
    VDB-->>RAG: تأكيد الحفظ
    RAG-->>UI: النظام جاهز
    UI-->>U: تم إضافة المستندات بنجاح
    
    Note over U,LLM: مرحلة الاستعلام
    U->>UI: طرح سؤال
    UI->>RAG: معالجة السؤال
    RAG->>EMB: تحويل السؤال إلى vector
    EMB->>VDB: البحث عن أقرب النتائج
    VDB-->>RAG: إرجاع النصوص المطابقة
    RAG->>LLM: إرسال السؤال + السياق
    LLM-->>RAG: توليد الإجابة
    RAG-->>UI: إرجاع الإجابة
    UI-->>U: عرض الإجابة النهائية
```

## شرح المخططات

### المخطط الأول: تدفق العمليات
- يوضح المسار الكامل من السؤال إلى الإجابة
- يشمل عملية إعداد النظام وإضافة المستندات
- يظهر نقاط القرار والمعالجة المختلفة

### المخطط الثاني: البنية المعمارية
- يقسم النظام إلى طبقات منطقية
- يوضح العلاقات بين المكونات
- يساعد في فهم التصميم العام للنظام

### المخطط الثالث: دورة حياة البيانات
- يوضح التفاعل بين المكونات عبر الزمن
- يشمل سيناريوهين: إضافة البيانات والاستعلام
- يساعد في فهم تسلسل العمليات

## كيفية استخدام المخططات

1. **للتطوير**: استخدم المخططات كمرجع أثناء كتابة الكود
2. **للاختبار**: حدد نقاط الاختبار من المخططات
3. **للتوثيق**: اشرح النظام للآخرين باستخدام هذه المخططات
4. **للتحسين**: حدد نقاط الاختناق والتحسين المحتملة
